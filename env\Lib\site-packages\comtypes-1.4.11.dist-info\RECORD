../../Scripts/clear_comtypes_cache.exe,sha256=ZVa9q9HT_xwyd_OnqwcHVOqy_hlg45MZIBKm2Nrl3fQ,108421
comtypes-1.4.11.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
comtypes-1.4.11.dist-info/METADATA,sha256=xKYzxjGN6205Vi2JrnamwLV5y4Scc82scMQvyUB66rE,7183
comtypes-1.4.11.dist-info/RECORD,,
comtypes-1.4.11.dist-info/WHEEL,sha256=QZxptf4Y1BKFRCEDxD4h2V0mBFQOVFLFEpvxHmIs52A,91
comtypes-1.4.11.dist-info/entry_points.txt,sha256=Qv2GxaRYrKn2xxEqjcg2BbqNzAP8PZp4rYfBUVpvag0,67
comtypes-1.4.11.dist-info/licenses/LICENSE.txt,sha256=Oxdn8BCYC0aSayO_Cvzl1y8zWe5eKye6ynG5tCCas4M,1273
comtypes-1.4.11.dist-info/top_level.txt,sha256=bxrAJZpBG5XMT64eKx908THCbupyI0ywd7cYIlx4Uco,9
comtypes/GUID.py,sha256=d78UkD3Q1atD3uRsiq0lfV6Tw6-9SjJT1093nXv8ZwA,3579
comtypes/__init__.py,sha256=LduZcpyJRidlVMFIVlNle9n6mU0hnmpP2OPquS6B76I,9010
comtypes/__pycache__/GUID.cpython-312.pyc,,
comtypes/__pycache__/__init__.cpython-312.pyc,,
comtypes/__pycache__/_comobject.cpython-312.pyc,,
comtypes/__pycache__/_memberspec.cpython-312.pyc,,
comtypes/__pycache__/_meta.cpython-312.pyc,,
comtypes/__pycache__/_npsupport.cpython-312.pyc,,
comtypes/__pycache__/_safearray.cpython-312.pyc,,
comtypes/__pycache__/_tlib_version_checker.cpython-312.pyc,,
comtypes/__pycache__/_vtbl.cpython-312.pyc,,
comtypes/__pycache__/automation.cpython-312.pyc,,
comtypes/__pycache__/clear_cache.cpython-312.pyc,,
comtypes/__pycache__/connectionpoints.cpython-312.pyc,,
comtypes/__pycache__/errorinfo.cpython-312.pyc,,
comtypes/__pycache__/git.cpython-312.pyc,,
comtypes/__pycache__/hresult.cpython-312.pyc,,
comtypes/__pycache__/logutil.cpython-312.pyc,,
comtypes/__pycache__/messageloop.cpython-312.pyc,,
comtypes/__pycache__/patcher.cpython-312.pyc,,
comtypes/__pycache__/persist.cpython-312.pyc,,
comtypes/__pycache__/safearray.cpython-312.pyc,,
comtypes/__pycache__/shelllink.cpython-312.pyc,,
comtypes/__pycache__/stream.cpython-312.pyc,,
comtypes/__pycache__/typeinfo.cpython-312.pyc,,
comtypes/__pycache__/util.cpython-312.pyc,,
comtypes/__pycache__/viewobject.cpython-312.pyc,,
comtypes/_comobject.py,sha256=IAQWONgzB3nhJy2kuGwCrndSwgCvYG18017Wvr34lGs,19644
comtypes/_memberspec.py,sha256=fvkblOD26Uq9uOXVx6rHPUgSl5flC18AUU8qR87iSVw,24888
comtypes/_meta.py,sha256=EckTJGcRdsiF9qRpeTps47ieEp5LHja_XwKYahh_-XE,3916
comtypes/_npsupport.py,sha256=7NtW97pYL3e15_jZ2WvP4IctYbGaPvEKuWUern9pUzY,5244
comtypes/_post_coinit/__init__.py,sha256=2NugBum--2JO6fvSm8wuO77V45LiCpjDd8emUVyA79I,727
comtypes/_post_coinit/__pycache__/__init__.cpython-312.pyc,,
comtypes/_post_coinit/__pycache__/_cominterface_meta_patcher.cpython-312.pyc,,
comtypes/_post_coinit/__pycache__/bstr.cpython-312.pyc,,
comtypes/_post_coinit/__pycache__/instancemethod.cpython-312.pyc,,
comtypes/_post_coinit/__pycache__/misc.cpython-312.pyc,,
comtypes/_post_coinit/__pycache__/unknwn.cpython-312.pyc,,
comtypes/_post_coinit/_cominterface_meta_patcher.py,sha256=Ul1XzOxu-sKc_FLW-1LmA-J7h5b9WxdI5sdn4oA2o1w,5883
comtypes/_post_coinit/bstr.py,sha256=R2dPCPq2mqcJfODshOHkwFaXxHcjRwSBnrmTWREvdhM,1288
comtypes/_post_coinit/instancemethod.py,sha256=p-DF-NM4DpKXWrRawQ9mW78zmT36qKWump-0RNOiXOE,369
comtypes/_post_coinit/misc.py,sha256=IVaR-QBJ-9JyLJMsZhaGCRgkuP9xQXmgeUaiVVMGCJY,11682
comtypes/_post_coinit/unknwn.py,sha256=4ZDqgRD91KZLBgUzJxMTXMOtP2qSAk17R5klClqTEDs,17435
comtypes/_safearray.py,sha256=toNpuKYP00DYr-d_y0v8QNysAnx7CmWjmndY2LK63z8,4558
comtypes/_tlib_version_checker.py,sha256=gxQNW5BhKch3Gds9mBSAnsFkceU4vWzMtYDhfDt28OA,616
comtypes/_vtbl.py,sha256=mUYfYGcjWSGbnTYk7ZWfCZUBbHNdJzgbjipMqzGcyKA,16069
comtypes/automation.py,sha256=o5KDuCERKbj7P101tlo-sndT2LXvTeQVm_NDbTR1BsM,35295
comtypes/clear_cache.py,sha256=J2MHIgMf9b3DLxR6uINg611cLM81IyVEzsk1R-5iZPs,1759
comtypes/client/__init__.py,sha256=rj0gMIyx_I1exIi5HbGVQut3xpPktLgyuHsKb1kWf5M,1223
comtypes/client/__pycache__/__init__.cpython-312.pyc,,
comtypes/client/__pycache__/_activeobj.cpython-312.pyc,,
comtypes/client/__pycache__/_code_cache.cpython-312.pyc,,
comtypes/client/__pycache__/_constants.cpython-312.pyc,,
comtypes/client/__pycache__/_create.cpython-312.pyc,,
comtypes/client/__pycache__/_events.cpython-312.pyc,,
comtypes/client/__pycache__/_generate.cpython-312.pyc,,
comtypes/client/__pycache__/_managing.cpython-312.pyc,,
comtypes/client/__pycache__/dynamic.cpython-312.pyc,,
comtypes/client/__pycache__/lazybind.cpython-312.pyc,,
comtypes/client/_activeobj.py,sha256=77jk2GCfqEsrbmQ9_qdoadufYRlQr5czxAxVFRBx9co,1679
comtypes/client/_code_cache.py,sha256=caS-IWJBTPZd6PtZJZcI2aEsmeMkn-rCeveOoLV7Lik,5405
comtypes/client/_constants.py,sha256=xaGaZluuo5Bmgbk2jFGhe_CtWv70JLwkwW0bwMknNAo,4577
comtypes/client/_create.py,sha256=4qwaQUwUbd3dvKEYNbvITGS1yQzipI58yoNB4WvQhoc,5644
comtypes/client/_events.py,sha256=X6BcH2vkSMMgtJ6A4noo9G1Gow0m3qWZVNzS3zMYnWk,13883
comtypes/client/_generate.py,sha256=xXQMWrfVk_2qiMZmUv92FwIh9uGjTxO3et9mv7zKS4A,11941
comtypes/client/_managing.py,sha256=9Pmru-17KxugccKc44KpDkabUoIKWqAyCou8Ucci7iU,4266
comtypes/client/dynamic.py,sha256=ZD3C89UK47udSWa79Mgiax_pq1uQV8RO5iTbTvv4T6I,6030
comtypes/client/lazybind.py,sha256=TzoaHFmic0LfSrruYWXhOKGcMiJK-ls2XRPHY_B8W8U,8924
comtypes/connectionpoints.py,sha256=TFLGNbFsISGMETZ2cbWfc7kyVQHOebT3vs6aaL0Sd1M,4669
comtypes/errorinfo.py,sha256=ucS2YmMWIJma-f5si-6pXCYU-bjH8z2OGv8oWuw3aF8,5929
comtypes/git.py,sha256=_LQHMM-c5a3ejxEI9Aq36_pC79ioXWtmOoqr0j4tDuc,2750
comtypes/hints.pyi,sha256=Z3NhkZ1PBMcz4bYL_eKck6izAolMJWon5vCHMhKx-Vs,11358
comtypes/hresult.py,sha256=83utS4zSO-q5wxWNeMvEfXbWJTdfk7kjQ0eDE-HYxb0,2997
comtypes/logutil.py,sha256=YLUooXhgUre2ehzWea_J1x2_23wlzCZ66Oz5t1ElX1k,2014
comtypes/messageloop.py,sha256=AYkft0yH7wxNxhOrkUYK9WqX8NnxlAeR5d-Wp9nAYTA,1664
comtypes/patcher.py,sha256=S4OHRtMn3y53okBWf_8o3BmLshOVlVT0Pu1PCgOgqIs,2046
comtypes/persist.py,sha256=m-ZGrVTvxg7pmZIvMt9TwqocPcrJTCy3b0m2QwI5ktg,8899
comtypes/safearray.py,sha256=6tcUgoZIjh7Vkr6AdB7cd4bNC44DcrC_H_5RgFZsVkE,17922
comtypes/server/__init__.py,sha256=fA_Ak2wNZ1jES4fdb5mWIMGdSjwEYxeBziGUyz_p93M,3156
comtypes/server/__pycache__/__init__.cpython-312.pyc,,
comtypes/server/__pycache__/automation.cpython-312.pyc,,
comtypes/server/__pycache__/connectionpoints.cpython-312.pyc,,
comtypes/server/__pycache__/inprocserver.cpython-312.pyc,,
comtypes/server/__pycache__/localserver.cpython-312.pyc,,
comtypes/server/__pycache__/register.cpython-312.pyc,,
comtypes/server/__pycache__/w_getopt.cpython-312.pyc,,
comtypes/server/automation.py,sha256=68mFW5Ool1Boa1IvaYAVh5Jwz6fRg1Qu2eBw4XrNj50,2885
comtypes/server/connectionpoints.py,sha256=S1kAueayYCwmPlYMtgeyBkD2RPyvQgRMZBrG0-yTPKY,6838
comtypes/server/inprocserver.py,sha256=a2PSEYRJ1Fx6xZAJxLI9LfHjal09xoBCfqNewG4IpBg,5026
comtypes/server/localserver.py,sha256=9yNzbbHMBfVTUIttb0cRibX6_DNdhbXOQDHZaotZ3is,3218
comtypes/server/register.py,sha256=OvQybbCtsx-JK-c0Z_B0r5xeSe8kuG-WqsJz4XqlO70,17241
comtypes/server/w_getopt.py,sha256=zYjIsVqECvHGdiuwYEWNtzOFeuZSefZr_uyBYrXoij0,1571
comtypes/shelllink.py,sha256=6ZLkXAP-KVE7WBsZcd3yhhsIp01ygiv9DKKlo853pf0,10933
comtypes/stream.py,sha256=Qnxu1rVjn53zv5dTMzbFBHp1Se21bQrEQO-JWULJE0k,2573
comtypes/test/TestComServer.idl,sha256=72f38383VDU7opBvh6Mc_iE95QC5rQ-vR9kPX81n-9M,2487
comtypes/test/TestComServer.py,sha256=fqPrNf40ko61X3pxjp9qJHHoNihI_WO8EtirwOpGN-4,5161
comtypes/test/TestComServer.tlb,sha256=fdwAtGi-2skbrTHBEoOImEg48rxglmeY0H5IuD5_3Sk,3560
comtypes/test/TestDispServer.idl,sha256=SKdlb77Kj7pxkTX1BRYV04htcq0PDBq59ei2lMFuSI4,1835
comtypes/test/TestDispServer.py,sha256=-1B20b4RB2QBtj5v6VS59mzj20Xj8EszgpRw_KGgQsU,3776
comtypes/test/TestDispServer.tlb,sha256=eKi5NpCurHwgql_zGzc1SawWjSMbDojMRyrVj_CrRao,2992
comtypes/test/__init__.py,sha256=CqWdme72O-oNMqZT5oLm0dM-wvtZngGOHcD37YJbFwE,8162
comtypes/test/__pycache__/TestComServer.cpython-312.pyc,,
comtypes/test/__pycache__/TestDispServer.cpython-312.pyc,,
comtypes/test/__pycache__/__init__.cpython-312.pyc,,
comtypes/test/__pycache__/find_memleak.cpython-312.pyc,,
comtypes/test/__pycache__/runtests.cpython-312.pyc,,
comtypes/test/__pycache__/setup.cpython-312.pyc,,
comtypes/test/__pycache__/test_BSTR.cpython-312.pyc,,
comtypes/test/__pycache__/test_DISPPARAMS.cpython-312.pyc,,
comtypes/test/__pycache__/test_GUID.cpython-312.pyc,,
comtypes/test/__pycache__/test_QueryService.cpython-312.pyc,,
comtypes/test/__pycache__/test_agilent.cpython-312.pyc,,
comtypes/test/__pycache__/test_avmc.cpython-312.pyc,,
comtypes/test/__pycache__/test_basic.cpython-312.pyc,,
comtypes/test/__pycache__/test_casesensitivity.cpython-312.pyc,,
comtypes/test/__pycache__/test_clear_cache.cpython-312.pyc,,
comtypes/test/__pycache__/test_client.cpython-312.pyc,,
comtypes/test/__pycache__/test_client_dynamic.cpython-312.pyc,,
comtypes/test/__pycache__/test_client_regenerate_modules.cpython-312.pyc,,
comtypes/test/__pycache__/test_collections.cpython-312.pyc,,
comtypes/test/__pycache__/test_comobject.cpython-312.pyc,,
comtypes/test/__pycache__/test_comserver.cpython-312.pyc,,
comtypes/test/__pycache__/test_createwrappers.cpython-312.pyc,,
comtypes/test/__pycache__/test_dict.cpython-312.pyc,,
comtypes/test/__pycache__/test_dispifc_records.cpython-312.pyc,,
comtypes/test/__pycache__/test_dispifc_safearrays.cpython-312.pyc,,
comtypes/test/__pycache__/test_dispinterface.cpython-312.pyc,,
comtypes/test/__pycache__/test_dyndispatch.cpython-312.pyc,,
comtypes/test/__pycache__/test_errorinfo.cpython-312.pyc,,
comtypes/test/__pycache__/test_excel.cpython-312.pyc,,
comtypes/test/__pycache__/test_findgendir.cpython-312.pyc,,
comtypes/test/__pycache__/test_getactiveobj.cpython-312.pyc,,
comtypes/test/__pycache__/test_hresult.cpython-312.pyc,,
comtypes/test/__pycache__/test_ie.cpython-312.pyc,,
comtypes/test/__pycache__/test_ienum.cpython-312.pyc,,
comtypes/test/__pycache__/test_imfattributes.cpython-312.pyc,,
comtypes/test/__pycache__/test_inout_args.cpython-312.pyc,,
comtypes/test/__pycache__/test_midl_safearray_create.cpython-312.pyc,,
comtypes/test/__pycache__/test_monikers.cpython-312.pyc,,
comtypes/test/__pycache__/test_msscript.cpython-312.pyc,,
comtypes/test/__pycache__/test_npsupport.cpython-312.pyc,,
comtypes/test/__pycache__/test_outparam.cpython-312.pyc,,
comtypes/test/__pycache__/test_persist.cpython-312.pyc,,
comtypes/test/__pycache__/test_pump_events.cpython-312.pyc,,
comtypes/test/__pycache__/test_recordinfo.cpython-312.pyc,,
comtypes/test/__pycache__/test_safearray.cpython-312.pyc,,
comtypes/test/__pycache__/test_sapi.cpython-312.pyc,,
comtypes/test/__pycache__/test_server.cpython-312.pyc,,
comtypes/test/__pycache__/test_server_register.cpython-312.pyc,,
comtypes/test/__pycache__/test_shelllink.cpython-312.pyc,,
comtypes/test/__pycache__/test_showevents.cpython-312.pyc,,
comtypes/test/__pycache__/test_storage.cpython-312.pyc,,
comtypes/test/__pycache__/test_stream.cpython-312.pyc,,
comtypes/test/__pycache__/test_subinterface.cpython-312.pyc,,
comtypes/test/__pycache__/test_typeannotator.cpython-312.pyc,,
comtypes/test/__pycache__/test_typeinfo.cpython-312.pyc,,
comtypes/test/__pycache__/test_urlhistory.cpython-312.pyc,,
comtypes/test/__pycache__/test_variant.cpython-312.pyc,,
comtypes/test/__pycache__/test_w_getopt.cpython-312.pyc,,
comtypes/test/__pycache__/test_win32com_interop.cpython-312.pyc,,
comtypes/test/__pycache__/test_wmi.cpython-312.pyc,,
comtypes/test/__pycache__/test_word.cpython-312.pyc,,
comtypes/test/find_memleak.py,sha256=qygFRGSLnhRz0blzQrFOkG2TJavH_VX7m6cK9tsaes0,2157
comtypes/test/mylib.idl,sha256=FVfMqxRSaLZCA3K3EFeNPibPv9KApSIPZnOowI2fVEM,1664
comtypes/test/mylib.tlb,sha256=Kvv8JW32DW7iCeBWiQo91D-21OYb7OMNuy5hYY2hPvg,3080
comtypes/test/mytypelib.idl,sha256=ghjqgtzlLYvsptCXq44AS7P85DPJwbLmqTBYJKBU62s,2590
comtypes/test/runtests.py,sha256=bI08Hq5XCaHM5NAVG1E3FSbMbE6td0-3z2kdhuZosWU,144
comtypes/test/setup.py,sha256=r28sSnwiFO4c6VTuxPHpEDSm9aHT9MKa_KktKzUGhOU,172
comtypes/test/test_BSTR.py,sha256=1hQ85usi6qm8exwPRWK6tJLBSVTQ9jlYpxjDjxtV0Nk,1208
comtypes/test/test_DISPPARAMS.py,sha256=P1VcwZ9k_p3JbK6oFn2Ld0AI413xiHDl3RD9Qjx9fe4,1156
comtypes/test/test_GUID.py,sha256=xnb4hywpPw1V_uc2vZoD7mKtlwb6iNqRIIS2MWa1WAc,1720
comtypes/test/test_QueryService.py,sha256=c4ulaokZtytySqBX7pygbReDemwRMWL3hlG5RGiXLOk,836
comtypes/test/test_agilent.py,sha256=VAzhjKVT83xFWenG8DwBAYv_q7MEpqWp8oTNqphGkvA,4399
comtypes/test/test_avmc.py,sha256=EyuLdBGpIGvjqdgGoHAsaIPIVpNl7DlIxvUfX55_5ts,1368
comtypes/test/test_basic.py,sha256=t11A2I6iWWIqzggCjinWSPrJgBmGhi9OcnhTNkL28yg,4695
comtypes/test/test_casesensitivity.py,sha256=PshS9mu9gZ0YWwAWrEjDOJneou_DkwsKbdtVTNP6YMk,1366
comtypes/test/test_clear_cache.py,sha256=RnXcAlyhX3QrbPdV0D-LX4WL6jsr-PpVYt43VmdxR4M,818
comtypes/test/test_client.py,sha256=6jc_JfRop9Tj3lafmpoqY-QUOogAhrxVjcjydUZBUxw,13295
comtypes/test/test_client_dynamic.py,sha256=ZQbbKsUAeo92uoy2zJG_uMCRD-9hOhSWds7Y92yVR28,2905
comtypes/test/test_client_regenerate_modules.py,sha256=2Bvaov6Tmm9VFE4JtHqb61BaCTxuXnGXnIbUyaqAu5E,7515
comtypes/test/test_collections.py,sha256=QmhxggPXD5g4GOq8MfKa8TRPRo1w-OPa3Vlkm1s9140,5481
comtypes/test/test_comobject.py,sha256=89wXfMnMivVOav0zilNrgBsi_p3skl8D8b2F8rGRoM4,5779
comtypes/test/test_comserver.py,sha256=YEdgBckMcyjqTvNdu6OO-ofrtwoxZWSYTIwwl34_NF4,12376
comtypes/test/test_createwrappers.py,sha256=qKXBnDxMCtMb5zuagXatwf2zyQBE-qwcGvUbsuoJ_xU,4098
comtypes/test/test_dict.py,sha256=eyecYBahyeoyk0R5gkP6Btql842ibXdAlmbr_t5lj54,3290
comtypes/test/test_dispifc_records.py,sha256=huvo7G16eTQixXYJNR9nLIAeZ6LV9vuibcS09jWhUvU,4277
comtypes/test/test_dispifc_safearrays.py,sha256=FBeaoyn3FgGw51KeE_vsj5M1c42s-9GM2bvA75FoEIk,3973
comtypes/test/test_dispinterface.py,sha256=K-PMxEaTP5pwfmRbuI506XCBgbVlksWObt9AytwxTw8,5109
comtypes/test/test_dyndispatch.py,sha256=TnITsx_-A0xtbaJtkqa8r3xCTTOVvcCty-zZtkVFS3U,3143
comtypes/test/test_errorinfo.py,sha256=MVi7Z7mXZgJmCXrlkvsV_57HQEH7Cx9AdsdIO1NGOgQ,4763
comtypes/test/test_excel.py,sha256=ay9eOsF3fJC6QKGHg5UVxnThk84HsN2DEwbiL03LMP4,5083
comtypes/test/test_findgendir.py,sha256=qXaZkWX7hLXuPNSZgyii1QadTXod23RSjPMDcNAuw9I,3032
comtypes/test/test_getactiveobj.py,sha256=S8uKu9k8Wvdg6jtr2yt7yIzW2l_Sqdr6S6GRwX7gHtE,1997
comtypes/test/test_hresult.py,sha256=AsGxJ2J_pwgYT26JB1E8x1u0yBFdFzftHIe0HKCkzio,1771
comtypes/test/test_ie.py,sha256=qqE-_Y8ki488vak7cJC8RGoRq7Njr1NLDJRWKsL-ln8,3839
comtypes/test/test_ienum.py,sha256=sl31YDmv-tpTR2MzCm190CKLjt0bk9Fz-U7mjfC0Xvo,887
comtypes/test/test_imfattributes.py,sha256=x9fJG0E5l7qnpwEhmkbSB3gvzkakmEvMeP8oaB9yxCM,1411
comtypes/test/test_inout_args.py,sha256=ae-wyZ78xbA6zVN9bTW_PfZNfLgbT3hSkE5KQb7S9o4,21040
comtypes/test/test_jscript.js,sha256=53vJJO4fDUBxIg3-D8Wuldzeejp3_MvImpN6yedPMTI,404
comtypes/test/test_midl_safearray_create.py,sha256=qzY1UWCCavMZaQWmUntwLvk_Cq10zhnn2YW1Tz6zC6Q,4141
comtypes/test/test_monikers.py,sha256=qkq6zK0xz3MZ2j4qOfGm6UjVjXwssJceZHYz22T3w0g,3218
comtypes/test/test_msscript.py,sha256=-eNtxb6lwUogW8qVraDQmKkEyxoK9AyKTu2nNe_x6D0,2879
comtypes/test/test_npsupport.py,sha256=xAjlvtforlPFdbDdJEDjmvVz_NRMbPu-dv5tuem6U9s,12209
comtypes/test/test_outparam.py,sha256=Zbxg_Mv2L16ISwdavWnLU9qz7vc8vWuBFKbz0_dqorY,3031
comtypes/test/test_persist.py,sha256=vxlKwy3uP0OM14dMYKeLamR8lWUTg6vaaRp_i8j6A2g,1270
comtypes/test/test_pump_events.py,sha256=wcOhzSgNkTBRZV_oywfO_sQeI9N-u3Kf9oQHhBmR1Dg,379
comtypes/test/test_recordinfo.py,sha256=cqCbX9LrJXaM7sry7nxOVblMhpbjBfodMNH91KNWCAQ,2723
comtypes/test/test_safearray.py,sha256=msMVveLT7rSzDOAU7l1j5Owc7ePR7xrJG74FpHkMHA0,6935
comtypes/test/test_sapi.py,sha256=qY8tpIOkD1WAgjkIk1CKHs_NT1-GTkxrFCCVZ6d79vI,1162
comtypes/test/test_server.py,sha256=PvEUZoZqCqLC4wEVzvdD6skqMZ8ZJUjQEOAzo5Tu24U,10765
comtypes/test/test_server_register.py,sha256=4Dm1RpRczFkW4Kog52f_xU3YOr5SrSHbnQNlwJJ6BMk,20039
comtypes/test/test_shelllink.py,sha256=HiNLstYY63ihmAHAReEy2fq1pIDaNgnksZnImDMq-DM,5113
comtypes/test/test_showevents.py,sha256=lXMiv9irnAKgGjXVYFWD1TsjjEyBhj8fB86f9T9lC0Y,1134
comtypes/test/test_storage.py,sha256=74S2PXbRMYih48tD-5d485hNldW7VxsnLmDGRfHXilA,5572
comtypes/test/test_stream.py,sha256=0oOcklhBq3H22crJtcR1Vgl10HqZ_YO0uPSgfWu6My4,4975
comtypes/test/test_subinterface.py,sha256=9uXpVLmO07vWe98RGWHLPga6Du6prSAa9ffvVe5-ZEk,383
comtypes/test/test_typeannotator.py,sha256=CUW4M2dhIGfQMNB4mPMDjR0ErMIos1W1D6kgt0ur7RM,6674
comtypes/test/test_typeinfo.py,sha256=JVgmvg_ZlF_ny8Xon11k7RKbFt1-6_SwG27B-QmzSPc,4691
comtypes/test/test_urlhistory.py,sha256=fMZH526ImG9vRbdDjym4pimCxpsM_Eed89xehS7yqCg,1854
comtypes/test/test_variant.py,sha256=K3kSu8pX_P0uwKpO-jm1TwHCWrhpC3gPeyzh6r9dB10,10260
comtypes/test/test_w_getopt.py,sha256=ubJA-Iow_DMz8Kpe3CIOnbxgq5bufSV45ul4KXTjJgc,1014
comtypes/test/test_win32com_interop.py,sha256=sBwnFaTj3y5vKhzpsQ_tCObIHP-dxH_O0ULesxXCvps,5853
comtypes/test/test_wmi.py,sha256=AjSH_ztPGoAxmycCWIvHF1PRUPNyXqBRBUOp4Nr7HpI,2314
comtypes/test/test_word.py,sha256=h3v0FUdVldEdDolJfJ6VGVi3KPEh2YOqdC0PM89BVq8,2372
comtypes/test/urlhist.tlb,sha256=u2RrUrgUD3R-sPRP8iBa6I3FcM4AyrbgCro64vSGZoU,6480
comtypes/tools/__init__.py,sha256=WgkiD-7kBZcJUSMiqdSjgeRiUgPK07vyu51IJ0mbfcA,30
comtypes/tools/__pycache__/__init__.cpython-312.pyc,,
comtypes/tools/__pycache__/tlbparser.cpython-312.pyc,,
comtypes/tools/__pycache__/typedesc.cpython-312.pyc,,
comtypes/tools/__pycache__/typedesc_base.cpython-312.pyc,,
comtypes/tools/codegenerator/__init__.py,sha256=gfLcwlpIxQhRAvzMdGLuO3Oljul65m4_YVEA3HRYEhg,207
comtypes/tools/codegenerator/__pycache__/__init__.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/codegenerator.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/comments.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/heads.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/helpers.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/modulenamer.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/namespaces.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/packing.cpython-312.pyc,,
comtypes/tools/codegenerator/__pycache__/typeannotator.cpython-312.pyc,,
comtypes/tools/codegenerator/codegenerator.py,sha256=ZRNKU4IVNJ5drE5YMEcZ0zMOug3D-R_2q-RhhHXCaNE,30228
comtypes/tools/codegenerator/comments.py,sha256=FrT-pfKNaZTOKxtozzcUkJD-R6DiRsA8rG-cFXe5wcE,3623
comtypes/tools/codegenerator/heads.py,sha256=rKhpg5aRR6dnApVQkGMxMKEYgP_pk84JWKdPOCfuGAk,8427
comtypes/tools/codegenerator/helpers.py,sha256=AMSx243_u5MLMtHVoabSXS6PuFMIn8MBqeNjeXF-TDE,12922
comtypes/tools/codegenerator/modulenamer.py,sha256=PMvrGAL6U-BbkfvnbI1X_Vv4_Tj7S5TJRLKfDcKTCYs,762
comtypes/tools/codegenerator/namespaces.py,sha256=NBCE0fJml6mssOf9KjduPghBCJl8UdrMmeD7a5FmScU,10782
comtypes/tools/codegenerator/packing.py,sha256=ITo5YWkEQLgOcp5OdUCdSZj9AHl4Fq-pEf0tWddHxok,2458
comtypes/tools/codegenerator/typeannotator.py,sha256=sXCvR9GQuM9skvQ5YUvEzZlmtjuZVO9cg9R6gChvxLc,14456
comtypes/tools/tlbparser.py,sha256=Xs83kGXYQEMjim0dwTdrqK81Fiu5ucmyJXvT1Jwb3HY,30808
comtypes/tools/typedesc.py,sha256=vQ62aLelllFBknUxDvZBH_teOp73LpWZJEXUhRMfgdo,6927
comtypes/tools/typedesc_base.py,sha256=9rY2EAEt36av9s1ZGdr5wAAcgn7zgtOpuDd5QVua7fA,6609
comtypes/typeinfo.py,sha256=qtY_VSHF1T4f8hMDAFRMyoIOptvMhHa_ZkhlcvPO4gI,45144
comtypes/util.py,sha256=ZmmgqC3hfraE99SGo22AoeVYxQMNW58cLOFFrS-hNi8,3007
comtypes/viewobject.py,sha256=zrAVkqG2caNx7FGnjtD5SRNPrWX3fEPZHs9YFPf3hxo,6502
